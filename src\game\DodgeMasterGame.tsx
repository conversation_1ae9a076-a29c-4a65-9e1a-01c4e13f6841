import React, { useEffect, useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import { GameEngine } from 'react-native-game-engine';
import { useGameStore } from './store/gameStore';
import { Player, Obstacle } from './types';
import { GAME_CONFIG, GAME_STATES } from './constants';

import {
  MovementSystem,
  CollisionSystem,
  SpawnSystem,
  ScoreSystem,
  resetSpawnSystem,
  resetScoreSystem,
} from './systems';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  PlayerController,
  ObstacleRenderer,
  ScoreRenderer,
  GameOverScreen,
  BackgroundRenderer,
  GameErrorBoundary,
} from './components';

export const DodgeMasterGame: React.FC = () => {
  const gameEngineRef = useRef<GameEngine>(null);
  const {
    score,
    highScore,
    gameStatus,
    isPlaying,
    startGame,
    gameOver,
    resetGame,
    loadHighScore,
  } = useGameStore();

  useEffect(() => {
    loadHighScore();
  }, [loadHighScore]);

  const createInitialEntities = () => {
    const player: Player = {
      id: 'player',
      type: 'player',
      position: {
        x: GAME_CONFIG.SCREEN_WIDTH / 2 - GAME_CONFIG.PLAYER_SIZE / 2,
        y: GAME_CONFIG.GAME_HEIGHT - GAME_CONFIG.PLAYER_SIZE - 50,
      },
      size: {
        width: GAME_CONFIG.PLAYER_SIZE,
        height: GAME_CONFIG.PLAYER_SIZE,
      },
      velocity: { x: 0, y: 0 },
    };

    const gameState = {
      score: 0,
      highScore,
      isPlaying: true,
      isGameOver: false,
      difficulty: 1,
      gameTime: 0,
    };

    return {
      player,
      gameState,
    };
  };

  const handlePlayerMove = (deltaX: number) => {
    if (gameEngineRef.current && isPlaying) {
      // Use the dispatch method from the game engine
      (gameEngineRef.current as any).dispatch({
        type: 'move-player',
        deltaX,
      });
    }
  };

  const handleGameEvent = (event: any) => {
    try {
      switch (event.type) {
        case 'game-over':
          gameOver();
          break;
        case 'move-player':
          // This will be handled by the movement system
          break;
        default:
          // Handle unknown events gracefully
          break;
      }
    } catch (error) {
      console.error('Error handling game event:', error);
    }
  };

  const handleRestart = () => {
    resetSpawnSystem();
    resetScoreSystem();
    startGame();
    
    if (gameEngineRef.current) {
      // Use the swap method from the game engine
      (gameEngineRef.current as any).swap(createInitialEntities());
    }
  };

  const handleBackToMenu = () => {
    resetGame();
  };

  const renderEntity = (entities: any) => {
    try {
      if (!entities) {
        return (
          <View style={styles.gameContainer}>
            <BackgroundRenderer />
            <ScoreRenderer
              score={0}
              highScore={highScore}
              position={{ x: 20, y: 40 }}
            />
          </View>
        );
      }

      const player = entities.player as Player;
      const obstacles = Object.keys(entities)
        .filter(key => key.startsWith('obstacle'))
        .map(key => entities[key] as Obstacle)
        .filter(obstacle => obstacle && obstacle.id); // Filter out invalid obstacles

      return (
        <View style={styles.gameContainer}>
          <BackgroundRenderer />

          <ScoreRenderer
            score={entities.gameState?.score || score}
            highScore={highScore}
            position={{ x: 20, y: 40 }}
          />

          {player && <PlayerRenderer player={player} />}

          {obstacles.map(obstacle => (
            <ObstacleRenderer key={obstacle.id} obstacle={obstacle} />
          ))}

          {gameStatus === GAME_STATES.GAME_OVER && (
            <GameOverScreen
              score={score}
              highScore={highScore}
              onRestart={handleRestart}
              onBackToMenu={handleBackToMenu}
            />
          )}
        </View>
      );
    } catch (error) {
      console.error('Error rendering game entities:', error);
      return (
        <View style={styles.gameContainer}>
          <BackgroundRenderer />
          <ScoreRenderer
            score={0}
            highScore={highScore}
            position={{ x: 20, y: 40 }}
          />
        </View>
      );
    }
  };

  if (gameStatus === GAME_STATES.MENU) {
    return (
      <GameErrorBoundary onReset={handleRestart}>
        <View style={styles.menuContainer}>
          <BackgroundRenderer />
          <GameOverScreen
            score={0}
            highScore={highScore}
            onRestart={handleRestart}
            onBackToMenu={() => {}}
          />
        </View>
      </GameErrorBoundary>
    );
  }

  return (
    <GameErrorBoundary onReset={handleRestart}>
      <View style={styles.container}>
        <PlayerController onPlayerMove={handlePlayerMove}>
          <GameEngine
            ref={gameEngineRef}
            style={styles.gameEngine}
            systems={[MovementSystem, CollisionSystem, SpawnSystem, ScoreSystem]}
            entities={createInitialEntities()}
            renderer={renderEntity}
            onEvent={handleGameEvent}
            running={isPlaying}
          />
        </PlayerController>
      </View>
    </GameErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  gameEngine: {
    flex: 1,
  },
  gameContainer: {
    flex: 1,
    position: 'relative',
  },
  menuContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
});
