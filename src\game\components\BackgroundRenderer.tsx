import React from 'react';
import { View, StyleSheet } from 'react-native';
import { COLORS, GAME_CONFIG } from '../constants';

export const BackgroundRenderer: React.FC = () => {
  return (
    <View style={styles.background} />
  );
};

const styles = StyleSheet.create({
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: GAME_CONFIG.SCREEN_WIDTH,
    height: GAME_CONFIG.GAME_HEIGHT,
    backgroundColor: COLORS.BACKGROUND,
  },
});
