import React from 'react';
import { View, StyleSheet } from 'react-native';
import { COLORS, GAME_CONFIG } from '../constants';

export const BackgroundRenderer: React.FC = () => {
  return (
    <View style={styles.background}>
      {/* Stars background */}
      <View style={styles.starsContainer}>
        {Array.from({ length: 30 }, (_, i) => (
          <View
            key={i}
            style={[
              styles.star,
              {
                left: Math.random() * GAME_CONFIG.SCREEN_WIDTH,
                top: Math.random() * GAME_CONFIG.GAME_HEIGHT,
                opacity: 0.2 + Math.random() * 0.8,
                width: 1 + Math.random() * 3,
                height: 1 + Math.random() * 3,
              },
            ]}
          />
        ))}
      </View>

      {/* Floating particles */}
      <View style={styles.particlesContainer}>
        {Array.from({ length: 10 }, (_, i) => (
          <View
            key={i}
            style={[
              styles.particle,
              {
                left: Math.random() * GAME_CONFIG.SCREEN_WIDTH,
                top: Math.random() * GAME_CONFIG.GAME_HEIGHT,
                opacity: 0.1 + Math.random() * 0.3,
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: GAME_CONFIG.SCREEN_WIDTH,
    height: GAME_CONFIG.GAME_HEIGHT,
    backgroundColor: '#1a1a2e', // Dark blue-purple background
  },
  starsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  star: {
    position: 'absolute',
    backgroundColor: '#ffffff',
    borderRadius: 2,
  },
  particlesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  particle: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: '#4CAF50',
    borderRadius: 2,
  },
});
