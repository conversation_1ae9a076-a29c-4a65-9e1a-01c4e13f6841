import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { COLORS, GAME_CONFIG } from '../constants';
import { formatScore } from '../utils';

interface GameOverScreenProps {
  score: number;
  highScore: number;
  onRestart: () => void;
  onBackToMenu: () => void;
}

export const GameOverScreen: React.FC<GameOverScreenProps> = ({
  score,
  highScore,
  onRestart,
  onBackToMenu,
}) => {
  const isNewHighScore = score >= highScore;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.gameOverText}>💀 GAME OVER 💀</Text>

        {isNewHighScore && (
          <Text style={styles.newHighScoreText}>🎉 NEW HIGH SCORE! 🎉</Text>
        )}

        <View style={styles.scoreContainer}>
          <Text style={styles.scoreLabel}>🎯 Score</Text>
          <Text style={styles.scoreValue}>{formatScore(score)}</Text>
        </View>

        <View style={styles.scoreContainer}>
          <Text style={styles.scoreLabel}>🏆 Best</Text>
          <Text style={styles.scoreValue}>{formatScore(highScore)}</Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={onRestart}>
            <Text style={styles.buttonText}>🔄 PLAY AGAIN</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={onBackToMenu}
          >
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>
              🏠 MENU
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    backgroundColor: COLORS.BACKGROUND,
    padding: 40,
    borderRadius: 20,
    alignItems: 'center',
    minWidth: 300,
  },
  gameOverText: {
    fontSize: GAME_CONFIG.GAME_OVER_FONT_SIZE,
    color: COLORS.TEXT,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  newHighScoreText: {
    fontSize: 18,
    color: '#FFD700', // Gold color
    fontWeight: 'bold',
    marginBottom: 20,
  },
  scoreContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 15,
  },
  scoreLabel: {
    fontSize: 20,
    color: COLORS.TEXT,
    fontWeight: '600',
  },
  scoreValue: {
    fontSize: 20,
    color: COLORS.TEXT,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    marginTop: 30,
    width: '100%',
  },
  button: {
    backgroundColor: COLORS.BUTTON,
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: COLORS.BUTTON,
  },
  buttonText: {
    color: COLORS.BUTTON_TEXT,
    fontSize: 18,
    fontWeight: 'bold',
  },
  secondaryButtonText: {
    color: COLORS.BUTTON,
  },
});
