import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { COLORS, GAME_CONFIG } from '../constants';
import { formatScore } from '../utils';

interface MainMenuScreenProps {
  highScore: number;
  onStartGame: () => void;
}

export const MainMenuScreen: React.FC<MainMenuScreenProps> = ({
  highScore,
  onStartGame,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.titleText}>DODGE MASTER</Text>
        <Text style={styles.subtitleText}>🎮 Survival Game 🎮</Text>

        <View style={styles.highScoreContainer}>
          <Text style={styles.highScoreLabel}>🏆 Best Score 🏆</Text>
          <Text style={styles.highScoreValue}>{formatScore(highScore)}</Text>
        </View>

        <TouchableOpacity style={styles.playButton} onPress={onStartGame}>
          <Text style={styles.playButtonText}>▶️ PLAY</Text>
        </TouchableOpacity>

        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionText}>📱 Tap left or right to move</Text>
          <Text style={styles.instructionText}>⚠️ Avoid falling obstacles!</Text>
          <Text style={styles.instructionText}>🎯 Survive as long as possible!</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    backgroundColor: COLORS.BACKGROUND,
    padding: 40,
    borderRadius: 20,
    alignItems: 'center',
    minWidth: 300,
  },
  titleText: {
    fontSize: 36,
    color: COLORS.TEXT,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: 16,
    color: COLORS.TEXT,
    opacity: 0.8,
    marginBottom: 30,
    textAlign: 'center',
  },
  highScoreContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  highScoreLabel: {
    fontSize: 18,
    color: COLORS.TEXT,
    fontWeight: '600',
    marginBottom: 5,
  },
  highScoreValue: {
    fontSize: 24,
    color: '#FFD700', // Gold color
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  playButton: {
    backgroundColor: COLORS.BUTTON,
    paddingVertical: 20,
    paddingHorizontal: 60,
    borderRadius: 15,
    marginBottom: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  playButtonText: {
    color: COLORS.BUTTON_TEXT,
    fontSize: 24,
    fontWeight: 'bold',
  },
  instructionsContainer: {
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 14,
    color: COLORS.TEXT,
    opacity: 0.7,
    textAlign: 'center',
    marginBottom: 5,
  },
});
