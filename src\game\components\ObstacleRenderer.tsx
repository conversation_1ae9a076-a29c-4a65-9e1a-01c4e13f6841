import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Obstacle } from '../types';
import { COLORS } from '../constants';

interface ObstacleRendererProps {
  obstacle: Obstacle;
}

export const ObstacleRenderer: React.FC<ObstacleRendererProps> = ({ obstacle }) => {
  if (!obstacle) return null;

  // Random obstacle types for variety
  const obstacleTypes = ['🪨', '⚡', '💥', '🔥', '⭐', '💎', '🌟', '⚠️'];
  // Use obstacle ID to ensure consistent emoji for each obstacle
  const obstacleIndex = obstacle.id ? parseInt(obstacle.id.replace('obstacle', ''), 10) % obstacleTypes.length : 0;
  const obstacleEmoji = obstacleTypes[obstacleIndex];

  return (
    <View
      style={[
        styles.obstacle,
        {
          left: obstacle.position.x,
          top: obstacle.position.y,
          width: obstacle.size.width,
          height: obstacle.size.height,
        },
      ]}
    >
      <Text style={styles.obstacleEmoji}>{obstacleEmoji}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  obstacle: {
    position: 'absolute',
    backgroundColor: COLORS.OBSTACLE,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  obstacleEmoji: {
    fontSize: 16,
    textAlign: 'center',
  },
});
