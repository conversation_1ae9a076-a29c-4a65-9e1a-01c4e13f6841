import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Obstacle } from '../types';
import { COLORS } from '../constants';

interface ObstacleRendererProps {
  obstacle: Obstacle;
}

export const ObstacleRenderer: React.FC<ObstacleRendererProps> = ({ obstacle }) => {
  if (!obstacle) return null;

  return (
    <View
      style={[
        styles.obstacle,
        {
          left: obstacle.position.x,
          top: obstacle.position.y,
          width: obstacle.size.width,
          height: obstacle.size.height,
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  obstacle: {
    position: 'absolute',
    backgroundColor: COLORS.OBSTACLE,
  },
});
