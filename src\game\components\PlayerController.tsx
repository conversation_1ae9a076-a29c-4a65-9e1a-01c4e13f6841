import React from 'react';
import { View, StyleSheet } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { GestureEvent } from '../types';

interface PlayerControllerProps {
  onPlayerMove: (deltaX: number) => void;
  children: React.ReactNode;
}

export const PlayerController: React.FC<PlayerControllerProps> = ({
  onPlayerMove,
  children,
}) => {
  const handleGestureEvent = (event: GestureEvent) => {
    const { translationX } = event.nativeEvent;

    // Calculate movement based on gesture
    const deltaX = translationX * 0.1; // Sensitivity factor
    onPlayerMove(deltaX);
  };

  const handleStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      // Reset movement when gesture ends
      onPlayerMove(0);
    }
  };

  return (
    <PanGestureHandler
      onGestureEvent={handleGestureEvent}
      onHandlerStateChange={handleStateChange}
      activeOffsetX={[-10, 10]} // Only respond to horizontal gestures
    >
      <View style={styles.container}>
        {children}
      </View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
