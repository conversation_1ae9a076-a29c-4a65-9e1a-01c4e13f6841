import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Player } from '../types';
import { COLORS } from '../constants';

interface PlayerRendererProps {
  player: Player;
}

export const PlayerRenderer: React.FC<PlayerRendererProps> = ({ player }) => {
  if (!player) return null;

  return (
    <View
      style={[
        styles.player,
        {
          left: player.position.x,
          top: player.position.y,
          width: player.size.width,
          height: player.size.height,
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  player: {
    position: 'absolute',
    backgroundColor: COLORS.PLAYER,
    borderRadius: 8,
  },
});
