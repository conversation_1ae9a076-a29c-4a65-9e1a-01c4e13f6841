import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Player } from '../types';
import { COLORS } from '../constants';

interface PlayerRendererProps {
  player: Player;
}

export const PlayerRenderer: React.FC<PlayerRendererProps> = ({ player }) => {
  if (!player) return null;

  return (
    <View
      style={[
        styles.player,
        {
          left: player.position.x,
          top: player.position.y,
          width: player.size.width,
          height: player.size.height,
        },
      ]}
    >
      <Text style={styles.playerEmoji}>🤖</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  player: {
    position: 'absolute',
    backgroundColor: COLORS.PLAYER,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  playerEmoji: {
    fontSize: 24,
    textAlign: 'center',
  },
});
