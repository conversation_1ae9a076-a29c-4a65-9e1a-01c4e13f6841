import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, GAME_CONFIG } from '../constants';
import { formatScore } from '../utils';

interface ScoreRendererProps {
  score: number;
  highScore: number;
  position: { x: number; y: number };
}

export const ScoreRenderer: React.FC<ScoreRendererProps> = ({
  score,
  highScore,
  position,
}) => {
  return (
    <View style={[styles.container, { top: position.y, left: position.x }]}>
      <Text style={styles.scoreText}>Score: {formatScore(score)}</Text>
      <Text style={styles.scoreText}>Best: {formatScore(highScore)}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: GAME_CONFIG.SCREEN_WIDTH - 40,
    paddingHorizontal: 20,
  },
  scoreText: {
    color: COLORS.TEXT,
    fontSize: GAME_CONFIG.SCORE_FONT_SIZE,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
});
