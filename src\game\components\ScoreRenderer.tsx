import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, GAME_CONFIG } from '../constants';
import { formatScore } from '../utils';

interface ScoreRendererProps {
  score: number;
  highScore: number;
  position: { x: number; y: number };
}

export const ScoreRenderer: React.FC<ScoreRendererProps> = ({
  score,
  highScore,
  position,
}) => {
  return (
    <View style={[styles.container, { top: position.y, left: position.x }]}>
      <View style={styles.scoreItem}>
        <Text style={styles.scoreText}>🎯 {formatScore(score)}</Text>
      </View>
      <View style={styles.scoreItem}>
        <Text style={styles.scoreText}>🏆 {formatScore(highScore)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: GAME_CONFIG.SCREEN_WIDTH - 40,
    paddingHorizontal: 20,
  },
  scoreItem: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  scoreText: {
    color: COLORS.TEXT,
    fontSize: GAME_CONFIG.SCORE_FONT_SIZE,
    fontWeight: 'bold',
    fontFamily: 'monospace',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});
