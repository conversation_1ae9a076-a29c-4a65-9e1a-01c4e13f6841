import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export const GAME_CONFIG = {
  // Screen dimensions
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  GAME_HEIGHT: SCREEN_HEIGHT * 0.8, // 80% of screen height for game area
  
  // Player settings
  PLAYER_SIZE: 40,
  PLAYER_SPEED: 8,
  PLAYER_COLOR: '#4CAF50', // Green
  
  // Obstacle settings
  OBSTACLE_WIDTH: 30,
  OBSTACLE_HEIGHT: 20,
  OBSTACLE_COLOR: '#FF5722', // Orange-red
  OBSTACLE_SPAWN_RATE: 1500, // milliseconds
  OBSTACLE_MIN_SPEED: 2,
  OBSTACLE_MAX_SPEED: 6,
  
  // Game settings
  BACKGROUND_COLOR: '#1a1a2e', // Dark blue-purple
  TEXT_COLOR: '#FFFFFF', // White
  SCORE_INCREMENT: 1, // Points per second
  DIFFICULTY_INCREASE_RATE: 0.1, // Difficulty increase per 10 seconds
  
  // Physics
  GRAVITY: 0.5,
  FRICTION: 0.98,
  
  // UI
  SCORE_FONT_SIZE: 24,
  GAME_OVER_FONT_SIZE: 32,
  BUTTON_HEIGHT: 50,
  BUTTON_WIDTH: 120,
};

export const COLORS = {
  PLAYER: GAME_CONFIG.PLAYER_COLOR,
  OBSTACLE: GAME_CONFIG.OBSTACLE_COLOR,
  BACKGROUND: GAME_CONFIG.BACKGROUND_COLOR,
  TEXT: GAME_CONFIG.TEXT_COLOR,
  BUTTON: '#2196F3', // Blue
  BUTTON_TEXT: '#FFFFFF',
};

export const GAME_STATES = {
  MENU: 'menu',
  PLAYING: 'playing',
  PAUSED: 'paused',
  GAME_OVER: 'game_over',
} as const;

export type GameStateType = typeof GAME_STATES[keyof typeof GAME_STATES];
