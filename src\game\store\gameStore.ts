import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GameState } from '../types';
import { GAME_STATES, GameStateType } from '../constants';

interface GameStore extends GameState {
  gameStatus: GameStateType;
  
  // Actions
  startGame: () => void;
  pauseGame: () => void;
  resumeGame: () => void;
  gameOver: () => void;
  resetGame: () => void;
  updateScore: (score: number) => void;
  updateDifficulty: (difficulty: number) => void;
  updateGameTime: (time: number) => void;
  loadHighScore: () => Promise<void>;
  saveHighScore: () => Promise<void>;
}

const INITIAL_STATE: GameState = {
  score: 0,
  highScore: 0,
  isGameOver: false,
  isPlaying: false,
  difficulty: 1,
  gameTime: 0,
};

const HIGH_SCORE_KEY = '@dodge_master_high_score';

export const useGameStore = create<GameStore>((set, get) => ({
  ...INITIAL_STATE,
  gameStatus: GAME_STATES.MENU,

  startGame: () => {
    set({
      ...INITIAL_STATE,
      gameStatus: GAME_STATES.PLAYING,
      isPlaying: true,
      isGameOver: false,
    });
  },

  pauseGame: () => {
    set({
      gameStatus: GAME_STATES.PAUSED,
      isPlaying: false,
    });
  },

  resumeGame: () => {
    set({
      gameStatus: GAME_STATES.PLAYING,
      isPlaying: true,
    });
  },

  gameOver: async () => {
    const { score, highScore, saveHighScore } = get();
    const newHighScore = Math.max(score, highScore);
    
    set({
      gameStatus: GAME_STATES.GAME_OVER,
      isPlaying: false,
      isGameOver: true,
      highScore: newHighScore,
    });

    if (score > highScore) {
      await saveHighScore();
    }
  },

  resetGame: () => {
    const { highScore } = get();
    set({
      ...INITIAL_STATE,
      gameStatus: GAME_STATES.MENU,
      highScore, // Keep the high score
    });
  },

  updateScore: (score: number) => {
    set({ score });
  },

  updateDifficulty: (difficulty: number) => {
    set({ difficulty });
  },

  updateGameTime: (time: number) => {
    set({ gameTime: time });
  },

  loadHighScore: async () => {
    try {
      const savedHighScore = await AsyncStorage.getItem(HIGH_SCORE_KEY);
      if (savedHighScore !== null) {
        set({ highScore: parseInt(savedHighScore, 10) });
      }
    } catch (error) {
      console.error('Failed to load high score:', error);
    }
  },

  saveHighScore: async () => {
    try {
      const { highScore } = get();
      await AsyncStorage.setItem(HIGH_SCORE_KEY, highScore.toString());
    } catch (error) {
      console.error('Failed to save high score:', error);
    }
  },
}));
