import { GameEngineUpdateEventOptionType } from 'react-native-game-engine';
import { Player, Obstacle } from '../types';
import { checkCollision } from '../utils';

export const CollisionSystem = (entities: any, { dispatch }: GameEngineUpdateEventOptionType) => {
  const player = entities.player as Player;
  const obstacles = Object.keys(entities)
    .filter(key => key.startsWith('obstacle'))
    .map(key => entities[key] as Obstacle);

  if (!player) return entities;

  // Check collisions between player and obstacles
  for (const obstacle of obstacles) {
    if (checkCollision(player, obstacle)) {
      // Dispatch game over event
      dispatch({ type: 'game-over' });
      break;
    }
  }

  return entities;
};
