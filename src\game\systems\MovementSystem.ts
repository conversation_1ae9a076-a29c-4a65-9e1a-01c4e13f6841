import { GameEngineUpdateEventOptionType } from 'react-native-game-engine';
import { Player, Obstacle } from '../types';
import { GAME_CONFIG } from '../constants';
import { clampToScreen } from '../utils';

export const MovementSystem = (entities: any, { events }: GameEngineUpdateEventOptionType) => {
  const player = entities.player as Player;
  const obstacles = Object.keys(entities)
    .filter(key => key.startsWith('obstacle'))
    .map(key => entities[key] as Obstacle);

  // Handle player movement events
  if (events && events.length > 0) {
    events.forEach((event: any) => {
      if (event.type === 'move-player' && player) {
        if (!player.velocity) {
          player.velocity = { x: 0, y: 0 };
        }
        player.velocity.x = event.deltaX * GAME_CONFIG.PLAYER_SPEED;
      }
    });
  }

  // Update player position if it has velocity
  if (player && player.velocity) {
    const newPosition = {
      x: player.position.x + player.velocity.x,
      y: player.position.y + player.velocity.y,
    };

    // Clamp player to screen bounds
    player.position = clampToScreen(newPosition, player.size);

    // Apply friction to velocity
    player.velocity.x *= GAME_CONFIG.FRICTION;
    player.velocity.y *= GAME_CONFIG.FRICTION;

    // Stop very small velocities
    if (Math.abs(player.velocity.x) < 0.1) player.velocity.x = 0;
    if (Math.abs(player.velocity.y) < 0.1) player.velocity.y = 0;
  }

  // Update obstacle positions
  obstacles.forEach(obstacle => {
    obstacle.position.y += obstacle.speed;

    // Remove obstacles that have fallen off screen
    if (obstacle.position.y > GAME_CONFIG.GAME_HEIGHT + obstacle.size.height) {
      delete entities[obstacle.id];
    }
  });

  return entities;
};
