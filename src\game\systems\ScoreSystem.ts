import { GameEngineUpdateEventOptionType } from 'react-native-game-engine';
import { GAME_CONFIG } from '../constants';
import { calculateDifficulty } from '../utils';

let lastScoreTime = 0;

export const ScoreSystem = (entities: any, { time }: GameEngineUpdateEventOptionType) => {
  const gameState = entities.gameState;
  
  if (!gameState || !gameState.isPlaying) {
    return entities;
  }

  const currentTime = time.current;
  
  // Update game time
  gameState.gameTime = currentTime;
  
  // Update score every second
  if (currentTime - lastScoreTime >= 1000) {
    gameState.score += GAME_CONFIG.SCORE_INCREMENT;
    lastScoreTime = currentTime;
  }
  
  // Update difficulty based on game time
  gameState.difficulty = calculateDifficulty(currentTime);

  return entities;
};

// Reset score timer when game restarts
export const resetScoreSystem = () => {
  lastScoreTime = 0;
};
