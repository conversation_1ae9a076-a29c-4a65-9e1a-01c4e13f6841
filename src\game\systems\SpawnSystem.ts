import { GameEngineUpdateEventOptionType } from 'react-native-game-engine';
import { Obstacle } from '../types';
import { GAME_CONFIG } from '../constants';
import { generateRandomX, generateId, calculateObstacleSpeed } from '../utils';

let lastSpawnTime = 0;

export const SpawnSystem = (entities: any, { time }: GameEngineUpdateEventOptionType) => {
  const currentTime = time.current;
  const gameState = entities.gameState;
  
  if (!gameState || !gameState.isPlaying) {
    return entities;
  }

  // Calculate spawn rate based on difficulty
  const difficulty = gameState.difficulty || 1;
  const spawnRate = Math.max(500, GAME_CONFIG.OBSTACLE_SPAWN_RATE - (difficulty - 1) * 100);

  // Check if it's time to spawn a new obstacle
  if (currentTime - lastSpawnTime > spawnRate) {
    const obstacleId = `obstacle_${generateId()}`;
    const obstacle: Obstacle = {
      id: obstacleId,
      type: 'obstacle',
      position: {
        x: generateRandomX(GAME_CONFIG.OBSTACLE_WIDTH),
        y: -GAME_CONFIG.OBSTACLE_HEIGHT, // Start above screen
      },
      size: {
        width: GAME_CONFIG.OBSTACLE_WIDTH,
        height: GAME_CONFIG.OBSTACLE_HEIGHT,
      },
      speed: calculateObstacleSpeed(difficulty),
    };

    entities[obstacleId] = obstacle;
    lastSpawnTime = currentTime;
  }

  return entities;
};

// Reset spawn timer when game restarts
export const resetSpawnSystem = () => {
  lastSpawnTime = 0;
};
