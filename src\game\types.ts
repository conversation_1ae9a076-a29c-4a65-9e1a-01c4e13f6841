export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Entity {
  id: string;
  position: Position;
  size: Size;
  velocity?: Position;
}

export interface Player extends Entity {
  type: 'player';
}

export interface Obstacle extends Entity {
  type: 'obstacle';
  speed: number;
}

export interface GameState {
  score: number;
  highScore: number;
  isGameOver: boolean;
  isPlaying: boolean;
  difficulty: number;
  gameTime: number;
}

export interface GameDimensions {
  width: number;
  height: number;
}

export interface TouchEvent {
  nativeEvent: {
    locationX: number;
    locationY: number;
  };
}

export interface GestureEvent {
  nativeEvent: {
    translationX: number;
    translationY: number;
    velocityX: number;
    velocityY: number;
  };
}
