import { Entity, Position, Size } from './types';
import { GAME_CONFIG } from './constants';

// Collision detection using AABB (Axis-Aligned Bounding Box)
export const checkCollision = (entity1: Entity, entity2: Entity): boolean => {
  return (
    entity1.position.x < entity2.position.x + entity2.size.width &&
    entity1.position.x + entity1.size.width > entity2.position.x &&
    entity1.position.y < entity2.position.y + entity2.size.height &&
    entity1.position.y + entity1.size.height > entity2.position.y
  );
};

// Generate random position for obstacles
export const generateRandomX = (entityWidth: number): number => {
  return Math.random() * (GAME_CONFIG.SCREEN_WIDTH - entityWidth);
};

// Keep entity within screen bounds
export const clampToScreen = (position: Position, size: Size): Position => {
  return {
    x: Math.max(0, Math.min(position.x, GAME_CONFIG.SCREEN_WIDTH - size.width)),
    y: Math.max(0, Math.min(position.y, GAME_CONFIG.GAME_HEIGHT - size.height)),
  };
};

// Generate unique ID for entities
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Calculate difficulty based on game time
export const calculateDifficulty = (gameTime: number): number => {
  return 1 + Math.floor(gameTime / 10000) * GAME_CONFIG.DIFFICULTY_INCREASE_RATE;
};

// Calculate obstacle speed based on difficulty
export const calculateObstacleSpeed = (difficulty: number): number => {
  const baseSpeed = GAME_CONFIG.OBSTACLE_MIN_SPEED;
  const maxSpeed = GAME_CONFIG.OBSTACLE_MAX_SPEED;
  const speed = baseSpeed + (difficulty - 1) * 0.5;
  return Math.min(speed, maxSpeed);
};

// Format score for display
export const formatScore = (score: number): string => {
  return score.toString().padStart(6, '0');
};

// Linear interpolation
export const lerp = (start: number, end: number, factor: number): number => {
  return start + (end - start) * factor;
};
