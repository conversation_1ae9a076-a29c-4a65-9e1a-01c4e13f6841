import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React from 'react';
import {APP_SCREEN, RootStackParamList} from './screen-type';
import {DodgeMasterScreen} from '../screens';
import {navigationRef} from './navigation-services';

export const Stack = createNativeStackNavigator<RootStackParamList>();

export const Navigations: React.FC = () => {
  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator initialRouteName={APP_SCREEN.DODGE_MASTER}>
        <Stack.Screen
          name={APP_SCREEN.DODGE_MASTER}
          component={DodgeMasterScreen}
          options={{headerShown: false}}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
