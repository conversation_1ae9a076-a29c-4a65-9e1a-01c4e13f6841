import React from 'react';
import {View, StyleSheet, StatusBar} from 'react-native';
import {DodgeMasterGame} from '../game/DodgeMasterGame';
import {APP_SCREEN, StackScreenProps} from '../navigators/screen-type';

type Props = StackScreenProps<APP_SCREEN.DODGE_MASTER>;

export const DodgeMasterScreen: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <StatusBar hidden />
      <DodgeMasterGame />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});
